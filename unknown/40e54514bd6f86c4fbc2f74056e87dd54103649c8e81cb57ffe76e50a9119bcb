import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import Constants from 'expo-constants';
import * as Updates from 'expo-updates';
import { useRouter } from 'expo-router'; 

const BASE_URL = Constants.expoConfig?.extra?.baseUrl;
const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedRole, setSelectedRole] = useState(null);
  const router = useRouter();

  const axiosInstance = axios.create({
    baseURL: BASE_URL,
  });

  // Request interceptor
  axiosInstance.interceptors.request.use(
    async (config) => {
      const token = await AsyncStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor to handle token expiration
  axiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
      // Check if error is due to unauthorized access (token expired)
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        console.log('Token expired or invalid - logging out');
        await handleLogout();
        return Promise.reject(error);
      }
      return Promise.reject(error);
    }
  );

  const handleLogout = async () => {
    try {
      await AsyncStorage.multiRemove([
        'token', 'roles', 'selectedRole', 'category', 'userId', 'requestId'
      ]);
      
      // Clear auth data from state
      setUser(null);
      setProfile(null);
      setSelectedRole(null);
      
      console.log('User logged out successfully due to expired token');
      
   
      router.replace('(auth)/welcome');
    } catch (error) {
      console.error('Error during forced logout:', error);
      

      setUser(null);
      setProfile(null);
      setSelectedRole(null);
      
     
      router.replace('(auth)/welcome');
    }
  };

  const refreshToken = async () => {
    try {
      const currentToken = await AsyncStorage.getItem('token');
      const userId = await AsyncStorage.getItem('userId');
      
      if (!currentToken || !userId) {
        console.error('Missing authentication data');
        await handleLogout();
        return null;
      }

      const response = await axios.post(`${BASE_URL}/api/user/refreshToken`, {
        userId,
      }, {
        headers: {
          Authorization: `Bearer ${currentToken}`,
        }
      });

      if (response.data.status === 'success' && response.data.data.token) {
        const newToken = response.data.data.token;
        await AsyncStorage.setItem('token', newToken);
        
        // Update user state with new token
        setUser(prev => ({
          ...prev,
          token: newToken,
        }));
        
        return newToken;
      } else {
        console.error('Token refresh failed: Invalid response');
        await handleLogout();
        return null;
      }
    } catch (error) {
      console.error('Failed to refresh token:', error);
      await handleLogout();
      return null;
    }
  };

  // Load auth state from AsyncStorage on app start
  useEffect(() => {
    const loadAuthState = async () => {
      try {
        const token = await AsyncStorage.getItem('token');
        const rolesStr = await AsyncStorage.getItem('roles');
        const category = await AsyncStorage.getItem('category');
        const userId = await AsyncStorage.getItem('userId');
        const requestId = await AsyncStorage.getItem('requestId');
        const storedSelectedRole = await AsyncStorage.getItem('selectedRole');

        if (token) {
          const roles = rolesStr ? JSON.parse(rolesStr) : [];
          setUser({
            token,
            roles,
            category,
            userId,
            requestId,
          });
          setSelectedRole(storedSelectedRole);

          // Fetch profile - if this fails due to auth issues, logout will be triggered
          const profileSuccess = await fetchUserProfile(token);
          if (!profileSuccess) {
            console.log('Failed to fetch profile during startup - logging out');
            await handleLogout();
          }
        }
      } catch (error) {
        console.error('Error loading auth state:', error);
        await handleLogout();
      } finally {
        setIsLoading(false);
      }
    };

    loadAuthState();
  }, []);

  // Setup token refresh on a regular schedule (every 2.5 hours)
  useEffect(() => {
    // Refresh token every 2.5 hours (9000000 ms)
    const tokenRefreshInterval = setInterval(async () => {
      const token = await AsyncStorage.getItem('token');
      if (token) {
        try {
          const result = await refreshToken();
          if (result) {
            console.log('Token refreshed on schedule');
          } else {
            console.log('Scheduled token refresh failed - user logged out');
          }
        } catch (error) {
          console.error('Scheduled token refresh failed:', error);
        }
      }
    }, 2.5 * 60 * 60 * 1000); // 2.5 hours in milliseconds

    return () => clearInterval(tokenRefreshInterval);
  }, []);

  const fetchUserProfile = async (token) => {
    try {
      const response = await axiosInstance.get('/api/user/profile', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
  
      if (response.data.status === 'success') {
        setProfile(response.data.data);
        return true;
      } else {
        console.error('Failed to fetch user profile:', response.data.message);
        return false;
      }
    } catch (error) {
      console.error('Error fetching user profile:', error.response?.data?.message || error.message);
      
      // If profile fetch fails due to auth issues (401/403), logout
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        console.log('Profile fetch failed due to auth issues - logging out');
        await handleLogout();
      }
      return false;
    }
  };

  const login = async (mobileNumber) => {
    if (!mobileNumber || mobileNumber.length !== 10) {
      throw new Error('Mobile number must be exactly 10 digits.');
    }
    if (!BASE_URL) {
      throw new Error('Base URL is not configured.');
    }

    setIsLoading(true);
    try {
      const response = await axios.post(`${BASE_URL}/api/user/login`, {
        mobileNumber,
      },
      {
        headers: {
          'Category': 'police',
        },
      });

      if (response.data.status === 'success') {
        return {
          userId: response.data.data.userId,
          requestId: response.data.data.requestId,
        };
      } else {
        throw new Error(response.data.message || 'Login failed.');
      }
    } catch (error) {
      throw new Error(
        error.response?.data?.message ||
          'An unexpected error occurred. Please try again later.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOtp = async (userId, requestId, otp) => {
    if (otp.length !== 5) {
      throw new Error('Please enter a valid 5-digit OTP');
    }

    const payload = { userId, requestId, otp };
    try {
      const response = await axios.post(`${BASE_URL}/api/user/login/verify`, payload);

      if (response.data.status === 'success') {
        const roles = response.data.data.role;
        const defaultRole = roles.includes('officer') ? 'officer' : roles[0];
        const token = response.data.data.token;

        await AsyncStorage.setItem('token', token);
        await AsyncStorage.setItem('roles', JSON.stringify(roles));
        await AsyncStorage.setItem('selectedRole', defaultRole);
        await AsyncStorage.setItem('category', response.data.data.category);
        await AsyncStorage.setItem('userId', userId);
        await AsyncStorage.setItem('requestId', requestId);

        setUser({
          token,
          roles,
          category: response.data.data.category,
          userId,
          requestId,
        });
        setSelectedRole(defaultRole);

        // Fetch and store profile data
        await fetchUserProfile(token);

        return true;
      } else {
        throw new Error(response.data.message || 'OTP verification failed');
      }
    } catch (error) {
      throw new Error(
        error.response?.data?.message ||
          'An error occurred while verifying OTP'
      );
    }
  };

  const logout = async () => {
    await handleLogout();
  };

  const updateSelectedRole = async (newRole) => {
    if (!user?.roles.includes(newRole)) {
      throw new Error('Invalid role selection.');
    }
    await AsyncStorage.setItem('selectedRole', newRole);
    setSelectedRole(newRole);
  };

  const updateUserProfile = async (updateData) => {
    try {
      // Validate allowed fields
      const allowedFields = ['name', 'emailId', 'mobileNumber', 'displayUrl'];
      const filteredData = {};
      
      // Only include allowed fields that are present in updateData
      allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          filteredData[field] = updateData[field];
        }
      });

      // Validate email format if email is being updated
      if (filteredData.emailId && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(filteredData.emailId)) {
        throw new Error('Invalid email format');
      }

      // Validate mobile number format if mobile is being updated
      if (filteredData.mobileNumber && !/^[0-9]{10}$/.test(filteredData.mobileNumber)) {
        throw new Error('Mobile number must be exactly 10 digits');
      }

      // Validate name if being updated
      if (filteredData.name && filteredData.name.trim().length < 2) {
        throw new Error('Name must be at least 2 characters long');
      }

      const response = await axiosInstance.put('/api/user/profile', filteredData);
      
      if (response.data.status === 'success') {
        setProfile(response.data.data);
        return true;
      } else {
        throw new Error(response.data.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating user profile:', error.response?.data?.message || error.message);
      throw error;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        profile,
        isLoading,
        login,
        verifyOtp,
        logout,
        selectedRole,
        updateSelectedRole,
        userId: user?.userId || null,
        requestId: user?.requestId || null,
        token: user?.token || null,
        refreshToken,
        updateUserProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);