import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Drawer } from 'expo-router/drawer';
import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import { DrawerContentScrollView, DrawerItemList } from '@react-navigation/drawer';
import { useAuth } from '../../../context/auth-context';
import { transformUrl } from '../../../utils/transformUrl';
import { router } from 'expo-router';

function CustomDrawerContent(props) {
  const { profile, selectedRole, logout } = useAuth();
  
  if (!profile) {
    return (
      <DrawerContentScrollView {...props}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
        <DrawerItemList {...props} />
      </DrawerContentScrollView>
    );
  }

  const { name, emailId, policeProfile, displayUrl } = profile;
  const transformedImageUrl = transformUrl(displayUrl);

  return (
    <DrawerContentScrollView 
      {...props}
      contentContainerStyle={styles.drawerContainer}
    >
      {/* Minimal Profile Section */}
      <View style={styles.profileSection}>
        {transformedImageUrl ? (
          <Image source={{ uri: transformedImageUrl }} style={styles.profileImage} />
        ) : (
          <View style={styles.profileImagePlaceholder}>
            <Text style={styles.profileImagePlaceholderText}>{name?.charAt(0) || "U"}</Text>
          </View>
        )}
        <View style={styles.profileTextContainer}>
          <Text style={styles.profileName}>{name}</Text>
          <Text style={styles.profileEmail}>{emailId}</Text>
          
          <View style={styles.roleChip}>
            <Text style={styles.roleText}>{selectedRole}</Text>
          </View>
          
          {policeProfile && (
            <View style={styles.designationChip}>
              <Text style={styles.designationText}>{policeProfile.designation}</Text>
            </View>
          )}
        </View>
      </View>

      <View style={styles.divider} />

      <View style={styles.navigationContainer}>
        <DrawerItemList {...props} />
      </View>
      
      <TouchableOpacity style={styles.logoutButton} onPress={logout}>
        <Ionicons name="log-out-outline" size={18} color="#fff" />
        <Text style={styles.logoutText}>Logout</Text>
      </TouchableOpacity>
    </DrawerContentScrollView>
  );
}

export default function DrawerLayoutOfficer() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Drawer
        drawerContent={(props) => <CustomDrawerContent {...props} />}
        screenOptions={{
          headerShown: false,
          drawerActiveBackgroundColor: '#0a34a1',
          drawerActiveTintColor: '#fff',
          drawerInactiveTintColor: '#333',
          drawerItemStyle: {
            borderRadius: 8,
            paddingLeft: 5,
          },
          drawerLabelStyle: {
            fontSize: 15,
            fontWeight: '500',
          },
        }}
      >
       <Drawer.Screen
  name="(tabs)"
  options={{
    drawerIcon: ({ color }) => (
      <Ionicons name="business-outline" size={22} color={color} />
    ),
    title: "Home",
  }}
  listeners={() => ({
    drawerItemPress: (e) => {
      e.preventDefault(); 

      router.replace('(officer)/(drawer)/(tabs)')
    },
  })}
/>
{/* <Drawer.Screen
  name="(tabs)/profile"
  options={{
    drawerIcon: ({ color }) => (
      <Ionicons name="business-outline" size={22} color={color} />
    ),
    title: "Home",
  }}
  listeners={() => ({
    drawerItemPress: (e) => {
      e.preventDefault(); 

      router.replace('(officer)/(drawer)/(tabs)/profile')
    },
  })}
/> */}
      </Drawer>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  drawerContainer: {
    flex: 1,
    backgroundColor: '#f8f9fb', // Light background for a clean look
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#0a34a1',
    fontWeight: '500',
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 30,
    backgroundColor: '#fff', // White card-like background
    borderBottomWidth: 1,
    borderBottomColor: '#e0e4ec',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 3, // Subtle shadow for depth
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: '#0a34a1',
  },
  profileImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#0a34a1',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImagePlaceholderText: {
    fontSize: 24,
    fontWeight: '700',
    color: '#fff',
  },
  profileTextContainer: {
    marginLeft: 15,
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 13,
    color: '#777',
    marginBottom: 8,
  },
  roleChip: {
    backgroundColor: '#e6efff',
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    marginBottom: 6,
  },
  roleText: {
    fontSize: 12,
    color: '#0a34a1',
    fontWeight: '600',
  },
  designationChip: {
    backgroundColor: '#0a34a1',
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  designationText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e4ec',
    marginHorizontal: 15,
    marginVertical: 10,
  },
  navigationContainer: {
    flex: 1,
    paddingTop: 10,
    paddingHorizontal: 10,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#d9534f',
    marginHorizontal: 20,
    marginVertical: 20,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoutText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 15,
    marginLeft: 10,
  },
});