import { View, Text, Image, StyleSheet, Modal, TouchableOpacity, ScrollView, StatusBar, SafeAreaView, TextInput, Alert } from 'react-native';
import React, { useState } from 'react';
import { useAuth } from '../../../../context/auth-context';
import { transformUrl } from '../../../../utils/transformUrl';
import * as Updates from 'expo-updates';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors } from '../../../../constants/colors';

const ProfileOfficer = () => {
  const { logout, profile, selectedRole, updateSelectedRole, updateUserProfile } = useAuth();
  const [isRoleModalVisible, setRoleModalVisible] = useState(false);
  const [newSelectedRole, setNewSelectedRole] = useState(selectedRole);

  const router = useRouter();

  const handleLogout = () => {
    logout();
  };

  const handleRoleChange = (role) => {
    setNewSelectedRole(role);
    setRoleModalVisible(false);
  };

  const handleSaveRole = async () => {
    await updateSelectedRole(newSelectedRole);
    try {
      await Updates.reloadAsync();
    } catch (error) {
      console.error('Failed to reload the app:', error);
    }
  };

  if (!profile) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading profile...</Text>
      </SafeAreaView>
    );
  }

  const {
    name,
    category,
    emailId,
    mobileNumber,
    policeProfile,
    roles,
    displayUrl,
  } = profile;

  const transformedImageUrl = transformUrl(displayUrl);

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style="light" />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* Enhanced Header with Gradient */}
        <LinearGradient
          colors={[Colors.primary, '#1E4FC7']}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            {transformedImageUrl ? (
              <Image source={{ uri: transformedImageUrl }} style={styles.headerProfileImage} />
            ) : (
              <View style={styles.headerProfileImagePlaceholder}>
                <Text style={styles.headerProfileImagePlaceholderText}>{name?.charAt(0) || "U"}</Text>
              </View>
            )}
            <Text style={styles.headerName}>{name}</Text>
            <Text style={styles.headerRole}>{selectedRole}</Text>
          </View>
        </LinearGradient>

        {/* Role Selector Card */}
        <View style={styles.roleSelectCard}>
          <TouchableOpacity 
            style={styles.roleChip}
            onPress={() => setRoleModalVisible(true)}
          >
            <Ionicons name="swap-horizontal" size={20} color="#fff" style={styles.roleChipIcon} />
            <Text style={styles.roleChipText}>Switch Role</Text>
          </TouchableOpacity>
        </View>

        {/* Enhanced Info Sections */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeaderRow}>
            <Ionicons name="person-circle-outline" size={24} color="#0B36A1" />
            <Text style={styles.sectionTitle}>Personal Information</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Ionicons name="person-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Full Name</Text>
              <Text style={styles.infoValue}>{name}</Text>
            </View>
          </View>
          
          {/* <View style={styles.infoItem}>
            <Ionicons name="briefcase-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Category</Text>
              <Text style={styles.infoValue}>{category}</Text>
            </View>
          </View> */}

          <View style={styles.infoItem}>
            <Ionicons name="mail-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Email</Text>
              <Text style={styles.infoValue}>{emailId}</Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <Ionicons name="call-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Mobile</Text>
              <Text style={styles.infoValue}>{mobileNumber}</Text>
            </View>
          </View>
        </View>

        {/* Enhanced Police Profile Section */}
        {policeProfile && (
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeaderRow}>
              <Ionicons name="shield-checkmark-outline" size={24} color="#0B36A1" />
              <Text style={styles.sectionTitle}>Professional Details</Text>
            </View>
            
            <View style={styles.infoItem}>
              <Ionicons name="shield-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Designation</Text>
                <Text style={styles.infoValue}>{policeProfile.designation}</Text>
              </View>
            </View>

            <View style={styles.infoItem}>
              <Ionicons name="business-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Department</Text>
                <Text style={styles.infoValue}>{policeProfile.department.name}</Text>
              </View>
            </View>

            <View style={styles.infoItem}>
              <Ionicons name="location-outline" size={20} color="#0B36A1" style={styles.infoIcon} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Police Station</Text>
                <Text style={styles.infoValue}>{policeProfile.policeStation.name}, {policeProfile.policeStation.city}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Enhanced Roles Section */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeaderRow}>
            <Ionicons name="key-outline" size={24} color="#0B36A1" />
            <Text style={styles.sectionTitle}>Roles & Permissions</Text>
          </View>
          <View style={styles.rolesContainer}>
            {roles.map((role) => (
              <View 
                key={role} 
                style={[
                  styles.roleTag,
                  role === selectedRole ? styles.selectedRoleTag : null
                ]}
              >
                <Ionicons 
                  name={role === selectedRole ? "checkmark-circle" : "ellipse-outline"} 
                  size={16} 
                  color={role === selectedRole ? "#fff" : "#0B36A1"} 
                  style={styles.roleTagIcon}
                />
                <Text 
                  style={[
                    styles.roleTagText,
                    role === selectedRole ? styles.selectedRoleTagText : null
                  ]}
                >
                  {role}
                </Text>
              </View>
            ))}
          </View>
        </View>

        {/* Enhanced Save Button */}
        {newSelectedRole !== selectedRole && (
          <TouchableOpacity 
            style={styles.saveButton} 
            onPress={handleSaveRole}
          >
            <LinearGradient
              colors={['#28a745', '#1f8f3a']}
              style={styles.saveButtonGradient}
            >
              <Ionicons name="refresh-outline" size={20} color="#fff" style={styles.saveButtonIcon} />
              <Text style={styles.saveButtonText}>Apply Changes & Reload</Text>
            </LinearGradient>
          </TouchableOpacity>
        )}
      </ScrollView>

      {/* Role Selection Modal */}
      <Modal
        visible={isRoleModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setRoleModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Role</Text>
              <TouchableOpacity 
                style={styles.closeButton} 
                onPress={() => setRoleModalVisible(false)}
              >
                <Ionicons name="close" size={24} color="#0B36A1" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.rolesList}>
              {roles.map((role) => (
                <TouchableOpacity
                  key={role}
                  style={[
                    styles.roleOption,
                    role === newSelectedRole ? styles.roleOptionSelected : null
                  ]}
                  onPress={() => handleRoleChange(role)}
                >
                  <Text 
                    style={[
                      styles.roleOptionText,
                      role === newSelectedRole ? styles.roleOptionTextSelected : null
                    ]}
                  >
                    {role}
                  </Text>
                  {role === newSelectedRole && (
                    <Ionicons name="checkmark" size={20} color="#fff" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.primary,
    fontFamily: 'Roboto',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  header: {
    paddingTop: 40,
    paddingBottom: 30,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerProfileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: Colors.background,
  },
  headerProfileImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: Colors.background,
  },
  headerName: {
    fontSize: 24,
    color: Colors.background,
    fontFamily: 'Roboto_bold',
    marginTop: 15,
  },
  headerRole: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 5,
  },
  roleSelectCard: {
    marginTop: -25,
    marginHorizontal: 20,
    backgroundColor: Colors.background,
    borderRadius: 15,
    padding: 15,
    elevation: 4,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  roleChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    justifyContent: 'center',
  },
  roleChipIcon: {
    marginRight: 8,
  },
  roleChipText: {
    color: Colors.background,
    fontWeight: '500',
    marginRight: 5,
    fontFamily: 'Roboto',
  },
  sectionContainer: {
    backgroundColor: Colors.background,
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 15,
    padding: 20,
    elevation: 2,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    color: Colors.primary,
    fontFamily: 'Roboto_bold',
    marginLeft: 10,
  },
  infoItem: {
    flexDirection: 'row',
    paddingVertical: 15,
    borderBottomWidth: 0.2,
    borderBottomColor: Colors.border,
  },
  infoIcon: {
    marginRight: 15,
    marginTop: 2,
    color: Colors.primary,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  infoValue: {
    fontSize: 15,
    color: Colors.black,
    marginTop: 2,
    fontFamily: 'Roboto',
  },
  rolesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  roleTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f4ff',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    marginBottom: 10,
  },
  roleTagIcon: {
    marginRight: 6,
  },
  roleTagText: {
    color: Colors.primary,
    fontSize: 13,
    fontFamily: 'Roboto',
  },
  selectedRoleTag: {
    backgroundColor: Colors.primary,
  },
  selectedRoleTagText: {
    color: Colors.background,
    fontFamily: 'Roboto_bold',
  },
  logoutButton: {
    flexDirection: 'row',
    backgroundColor: '#d9534f',
    marginHorizontal: 20,
    marginTop: 15,
    marginBottom: 30,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoutIcon: {
    marginRight: 10,
  },
  logoutText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 15,
    fontFamily: 'Roboto_bold',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: Colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 25,
    minHeight: '40%',
    maxHeight: '75%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
    fontFamily: 'Roboto_bold',
  },
  closeButton: {
    padding: 5,
  },
  rolesList: {
    paddingHorizontal: 20,
  },
  roleOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    paddingHorizontal: 10,
  },
  roleOptionSelected: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    marginVertical: 3,
    borderBottomWidth: 0,
  },
  roleOptionText: {
    fontSize: 16,
    color: Colors.black,
    fontFamily: 'Roboto',
  },
  roleOptionTextSelected: {
    color: Colors.background,
    fontWeight: '500',
    fontFamily: 'Roboto_bold',
  },
  saveButton: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 30,
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
  },
  saveButtonIcon: {
    marginRight: 8,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Roboto_bold',
  },
});

export default ProfileOfficer;